import tailwindcss from "@tailwindcss/vite";

export default ({ command }) => ({
	plugins: [tailwindcss()],
	base: command === "serve" ? "" : "/dist/",
	publicDir: "src/public",
	build: {
		outDir: "web/dist/",
		emptyOutDir: true,
		sourcemap: true,
		manifest: "manifest.json",
		minify: "esbuild",
		rollupOptions: {
			input: {
				index: "./src/index.js",
			},
			output: {
				dir: "web/dist/",
			},
		},
	},
	server: {
		fs: {
			strict: false,
		},
		host: "0.0.0.0",
		origin: "http://localhost:3000",
		port: 3000,
		strictPort: true,
		cors: {
			origin: ["https://burhan-tekin.ddev.site", "http://localhost:3000"],
			credentials: true,
		},
		headers: {
			"Access-Control-Allow-Origin": "*",
			"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
	},
});
