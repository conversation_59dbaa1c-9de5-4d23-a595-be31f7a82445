dateModified: 1756727973
email:
  fromEmail: b.wagne<PERSON>@puka.studio
  fromName: '<PERSON><PERSON><PERSON>'
  transportType: craft\mail\transportadapters\Sendmail
fs:
  publicUploads:
    hasUrls: true
    name: 'Public Uploads'
    settings:
      path: $ASSETS_FS_PATH
    type: craft\fs\Local
    url: $ASSETS_BASE_URL
meta:
  __names__:
    0ceafd0e-3e12-4210-8bca-b88f92821066: 'Rich Text' # Rich Text
    01ad964c-c91f-400d-b99a-0347eabc5076: Uploads # Uploads
    6a8dc492-6fb7-4fda-a8a1-9562406f67d5: Hero # Hero
    6a337d87-a9f1-46b1-82f8-9fe1e207b016: ButtonUrl # ButtonUrl
    6eb9f48c-c13c-4a73-9edf-f22d1e833f11: Default # Default
    17e5603b-057d-41d0-9d8f-7afa89a5aa90: '<PERSON><PERSON><PERSON>' # <PERSON><PERSON><PERSON>
    49df8875-811b-4d18-8b70-f387e1047bdb: 'Image Position' # Image Position
    61e81aff-fa63-488e-8909-1d796edd7687: Home # Home
    65d086e6-da19-495b-8b1c-fdaf01838479: Pages # Pages
    77ef78e1-9d09-495d-b223-c9e4f0fb6c7e: Body # Body
    96dc7da1-6517-4719-9ad0-42e156759e28: 'Public Schema' # Public Schema
    473f06bd-1ddc-40ce-b1e7-0146e69d7c31: 'Background Image' # Background Image
    550d8241-819c-42f5-bcf8-47fd9f6016fc: 'Button Text' # Button Text
    836f53cd-ec58-4d3a-a121-266d5d5f2979: 'Content Blocks' # Content Blocks
    6664d65d-e07d-4409-89f8-b5752f9b5a7b: 'Image With Text' # Image With Text
    caf7a6b0-645a-4e99-babb-68e1dada7cc6: 'Burhan Tekin' # Burhan Tekin
    d62a9951-5dc6-4ad2-9701-6e3d8909e8e9: Subtitle # Subtitle
    e2608f55-b09a-44e7-a593-42ba1a9932f8: Image # Image
system:
  edition: solo
  live: true
  name: 'Burhan Tekin'
  schemaVersion: 5.8.0.3
  timeZone: America/Los_Angeles
users:
  allowPublicRegistration: false
  defaultGroup: null
  photoSubpath: null
  photoVolumeUid: null
  require2fa: false
  requireEmailVerification: true
