{% macro img(asset, args = {}) %}
  {% if not asset %}{% endif %}

  {# default transform options #}
  {% set w = args.width  ?? 1600 %}
  {% set q = args.quality ?? 82 %}
  {% set fit = args.mode ?? 'crop' %}

  {% set url = asset.getUrl({ width: w, quality: q, mode: fit }) %}
  <img
    src="{{ url }}"
    alt="{{ (args.alt ?? asset.title)|e }}"
    loading="lazy"
    decoding="async"
    width="{{ asset.width }}"
    height="{{ asset.height }}"
  >
{% endmacro %}
