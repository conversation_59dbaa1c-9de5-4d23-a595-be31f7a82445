<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>{{ entry.title ?? siteName }}</title>

  {% if craft.app.config.env == 'dev' %}
    {# Development: Load Vite dev server #}
    <script type="module" src="http://localhost:5173/@vite/client"></script>
    <script type="module" src="http://localhost:5173/src/main.js"></script>
  {% else %}
    {# Production: Load built assets #}
    {{ craft.vite.script('src/main.js') }}
    {{ craft.vite.css('src/main.js') }}
  {% endif %}
</head>
<body>
  <main id="main">
    {% block content %}{% endblock %}
  </main>
</body>
</html>