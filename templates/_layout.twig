<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>{{ entry.title ?? siteName }}</title>

  {% if craft.app.config.env == 'dev' %}
    {# Development: Load Vite dev server #}
    <script type="module" src="http://localhost:3000/@vite/client"></script>
    <script type="module" src="http://localhost:3000/src/index.js"></script>
  {% else %}
    {# Production: Load built assets #}
    <link rel="stylesheet" href="/dist/assets/index.css">
    <script type="module" src="/dist/assets/index.js"></script>
  {% endif %}
</head>
<body>
  <main id="main">
    {% block content %}{% endblock %}
  </main>
</body>
</html>