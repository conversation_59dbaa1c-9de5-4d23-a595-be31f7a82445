<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>{{ entry.title ?? siteName }}</title>

  {{ craft.vite.script('src/index.js', false) }}

  {% if craft.app.config.env == 'dev' %}
    {# Development: Load Vite dev server #}
    <script type="module" src="http://localhost:3000/@vite/client"></script>
    <script type="module" src="http://localhost:3000/src/index.js"></script>
  {% else %}
    {# Production: Load built assets #}
    {{ craft.vite.script('src/index.js') }}
    {{ craft.vite.css('src/index.js') }}
  {% endif %}
</head>
<body>
  <main id="main">
    {% block content %}{% endblock %}
  </main>
</body>
</html>