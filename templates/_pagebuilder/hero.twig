{% import "_macros/image.twig" as m %}
{% set img = mb.backgroundImage.one() %}

<section class="hero" style="position:relative;overflow:hidden;">
  {% if img %}
    <div class="hero__bg" aria-hidden="true">
      {{ m.img(img, { width: 1600 }) }}
    </div>
  {% endif %}

  <div class="hero__content" style="position:relative;max-width:72rem;margin:0 auto;padding:4rem 1rem;text-align:center;">
    {% if mb.title %}<h1>{{ mb.title }}</h1>{% endif %}
    {% if mb.subtitle %}<p>{{ mb.subtitle }}</p>{% endif %}
    {% if mb.buttonText and mb.buttonUrl %}
      <p style="margin-top:1rem;">
        <a href="{{ mb.buttonUrl }}" class="btn">{{ mb.buttonText }}</a>
      </p>
    {% endif %}
  </div>
</section>
