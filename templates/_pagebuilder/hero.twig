{% import "_macros/image.twig" as m %}
{% set img = mb.backgroundImage.one() %}

<section class="relative overflow-hidden">
  {% if img %}
    <div class="absolute inset-0 -z-10" aria-hidden="true">
      {{ m.img(img, { width: 1600 }) }}
    </div>
  {% endif %}

  <div class="relative max-w-6xl mx-auto px-4 py-16 text-center">
    {% if mb.title %}<h1 class="text-4xl md:text-6xl font-bold text-white mb-4">{{ mb.title }}</h1>{% endif %}
    {% if mb.subtitle %}<p class="text-xl text-white/90 mb-8">{{ mb.subtitle }}</p>{% endif %}
    {% if mb.buttonText and mb.buttonUrl %}
      <div class="mt-8">
        <a href="{{ mb.buttonUrl }}" class="inline-block px-6 py-3 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors">{{ mb.buttonText }}</a>
      </div>
    {% endif %}
  </div>
</section>
