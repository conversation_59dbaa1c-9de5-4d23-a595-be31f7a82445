{% import "_macros/image.twig" as m %}
{% set img = mb.image.one() %}
{% set pos = (mb.imagePosition ?? 'left') %}

<section class="section">
  <div class="container" style="display:grid;gap:2rem;grid-template-columns:repeat(12,minmax(0,1fr));align-items:center;">
    <div style="grid-column:span 6; order: {{ pos == 'right' ? 2 : 1 }}">
      {% if img %}{{ m.img(img, { width: 1600 }) }}{% endif %}
    </div>
    <div style="grid-column:span 6; order: {{ pos == 'right' ? 1 : 2 }}">
      {{ mb.body|raw }}
    </div>
  </div>
</section>