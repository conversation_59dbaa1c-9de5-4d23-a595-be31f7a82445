{% import "_macros/image.twig" as m %}
{% set img = mb.image.one() %}
{% set pos = (mb.imagePosition ?? 'left') %}

<section class="py-12">
  <div class="max-w-6xl mx-auto px-4 grid grid-cols-12 gap-8 items-center">
    <div class="col-span-12 md:col-span-6 {{ pos == 'right' ? 'md:order-2' : 'md:order-1' }}">
      {% if img %}{{ m.img(img, { width: 1600 }) }}{% endif %}
    </div>
    <div class="col-span-12 md:col-span-6 {{ pos == 'right' ? 'md:order-1' : 'md:order-2' }}">
      <div class="prose prose-lg max-w-none">
        {{ mb.body|raw }}
      </div>
    </div>
  </div>
</section>